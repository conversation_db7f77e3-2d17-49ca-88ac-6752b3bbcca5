<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue Basics App</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
</head>
<body>
    <div id="nav" class="w-full bg-gray-300 h-20 flex justify-start items-center gap-2">
        <a href="#" class="text-xl text-center font-bold w-40">Vue App</a>
        <ul class="flex justify-center items-center gap-4 ">
            <li v-for="(page, index) in pages" :key="index"><a :href="page.link.url" @click.prevent="activePage = index" class="text-xl px-4" :title="`This link goes to ${page.link.name} page.`">{{ page.link.name }}</a></li>
        </ul>
    </div>
    <div id="app" class="container mx-auto pt-8" :key="index">
        <h1 class="text-3xl font-bold">{{ pages[activePage].title }}</h1>
        <p class="text-xl">{{ pages[activePage].content }}</p>
    </div>
    <script>
        Vue.createApp({
            data() {
                return {
                    activePage: 0,
                    pages: [
                        {
                            link: { name: 'Home', url: 'index.html' },
                            title: 'Home Page',
                            content: 'This Is Home Page Content.'
                        },
                        {
                            link: { name: 'About', url: 'index.html' },
                            title: 'About Page',
                            content: 'This Is About Page Content.'
                        },
                        {
                            link: { name: 'Contact', url: 'index.html' },
                            title: 'Contact Page',
                            content: 'This Is Contact Page Content.'
                        },
                    ],
                }
            }
        }).mount('body');
    </script>
</body>
</html>